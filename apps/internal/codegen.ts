import { type CodegenConfig } from "@graphql-codegen/cli"

const config: CodegenConfig = {
  watch: true,
  ignoreNoDocuments: true, // for better experience with the watcher
  generates: {
    "./src/federatedGql/": {
      preset: "client",
      documents: [
        "src/**/federated/mutations.ts",
        "src/**/federated/queries.ts",
      ],
      schema: "https://admin.eproc.dev/graphql",
      // presetConfig: {
      //   persistedDocuments: {
      //     hashAlgorithm: "sha256",
      //   },
      //   embedHashInDocument: true,
      // },
    },
  },
}

export default config
