import { cookies } from "next/headers"
import { getAccessTokenHeader } from "../accessToken"
import { EXPIRED_TIME_TOKEN_COOKIE_NAME } from "../authentication"
import { getUnixTime } from "date-fns"
import { type RequestMiddleware } from "graphql-request"

export type TRequest = Parameters<RequestMiddleware>[0] & {
  headers: Record<string, string>
}

export type TSession = { token: { accessToken: string } }

type TGraphqlRscMiddleware = {
  request: TRequest
  getServerSession: () => Promise<TSession | null>
}

const getNewestToken = async (
  getServerSession: () => Promise<TSession | null>
) => {
  const expTimeToken = parseInt(
    cookies().get(EXPIRED_TIME_TOKEN_COOKIE_NAME)?.value ?? ""
  )

  const tokenCookies = getAccessTokenHeader()?.stringFormat || ""
  const currentTime = getUnixTime(new Date())
  const timeDifference = expTimeToken - currentTime
  const isOneMinuteDifference = timeDifference <= 60

  if (isOneMinuteDifference || !expTimeToken || !tokenCookies) {
    const newSession = await getServerSession()
    return `gtp.accessToken=${newSession?.token?.accessToken};`
  }

  return tokenCookies
}

const graphqlRscMiddleware = async ({
  getServerSession,
  request,
}: TGraphqlRscMiddleware): Promise<TRequest> => {
  const token = await getNewestToken(getServerSession)

  request.headers["Content-Type"] = "application/json"
  request.headers["Cookie"] = token || ""

  return request
}

export default graphqlRscMiddleware
